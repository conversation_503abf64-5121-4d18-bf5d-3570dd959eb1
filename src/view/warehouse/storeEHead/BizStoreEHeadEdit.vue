<template>
  <section  >
    <a-card size="small" title="出库回单表头" class="cs-card-form">
      <a-spin :spinning="spinning"  >
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '160px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="storeENo" :label="'出库回单编号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.storeENo"/>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.purchaseOrderNo"/>
          </a-form-item>
          <a-form-item name="consignee" :label="'提货人'" class="grid-item" :colon="false">
            <cs-select :disabled="props.operationStatus === editStatus.SHOW || !isEdit "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.consignee" id="consignee">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="deliveryDate" :label="'出库日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="props.operationStatus === editStatus.SHOW || !isEdit"
              v-model:value="formData.deliveryDate"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="productAmountTotal" :label="'金额'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.productAmountTotal" notConvertNumber decimal int-length="14" precision="5"/>
          </a-form-item>
          <a-form-item name="tariffPrice" :label="'关税'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.tariffPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="insuranceFee" :label="'保险费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.insuranceFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="agentFee" :label="'代理费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.agentFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="props.operationStatus === editStatus.SHOW || !isEdit"
              v-model:value="formData.businessDate"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="sendFinance" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="props.operationStatus === editStatus.SHOW || !isEdit"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendFinance" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="redFlush" :label="'是否红冲'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.redFlush" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="status" :label="'数据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" v-if="!(props.operationStatus === editStatus.SHOW)"
                      :loading="handlerSaveLoading" :disabled="!isEdit">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:confirm']">
              <a-button v-if="!(props.operationStatus === editStatus.SHOW)" size="small"
                        @click="confirmOrderHead" class="cs-margin-right"
                        v-show="props.editConfig.editStatus !== editStatus.SHOW " :loading="confirmLoading">
                <template #icon>
                  <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;color:green"/>
                </template>
                <template #default>
                  确认
                </template>
              </a-button>
            </div>
            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:redFlush']">
              <a-button size="small" :loading="redFlushLoading" @click="handlerRedFlush" v-if="!(props.operationStatus === editStatus.SHOW)"
                        v-show="props.editConfig.editStatus !== editStatus.SHOW ">
                <template #icon>
                  <GlobalIcon type="up" style="color:red"/>
                </template>
                红冲
              </a-button>
            </div>
          </div>
        </a-form>
      </div>
      </a-spin>
    </a-card>

    <a-card size="small" title="出库回单表体" class="cs-card-form">
      <BizStoreEListList  :head-id="eHeadId" :is-edit="isEdit" :showDisable="showDisable" :unit-options="unitOptions" ref="orderListRef"/>
    </a-card>

  </section>
</template>

<script setup>
import {editStatus,productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, watch} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {
  confirmStoreEHead,
  getOrderSupplierList,
  getStoreEHeadByHeadSid,
  insertStoreEHeadList,
  redFlushStoreEHead,
  updateStoreEHeadList
} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import 'vue-multiselect/dist/vue-multiselect.min.css';
import CsSelect from "@/components/select/CsSelect.vue";
import BizStoreEListList from "@/view/warehouse/storeEHead/list/BizStoreEListList";

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default: () => ''
  },
  eHeadId:{
    type:String,
    default: () => ''
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  },
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)
const handlerSaveLoading = ref(false)
const spinning = ref(true);

// 表单数据
const formData = reactive({
  sid:'',
  storeENo: '',
  contractNo: '',
  purchaseOrderNo: '',
  consignee: '',
  deliveryDate: '',
  productAmountTotal: '',
  tariffPrice: '',
  insuranceFee: '',
  agentFee: '',
  businessDate: '',
  sendFinance: '0',
  status: '0',
  createrBy: '',
  createrUserName: '',
  createrTime: '',
  redFlush:''
})
// 校验规则
const rules = {

  storeENo: [
    { max: 80, message: '入库回单编号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '入库回单编号不能为空！', trigger: 'blur' },
  ],
  contractNo: [
    { max: 80, message: '合同号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '合同号不能为空！', trigger: 'blur' },
  ],
  purchaseOrderNo: [
    { max: 80, message: '进货单号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '进货单号不能为空！', trigger: 'blur' },
  ],
  consignee: [
    { required: true, message: '提货人不能为空！', trigger: 'blur' },
  ],
  productAmountTotal: [
    { required: true, message: '金额不能为空！', trigger: 'blur' },
  ],
  tariffPrice: [
    { required: true, message: '关税不能为空！', trigger: 'blur' },
  ],
  sendFinance: [
    { required: true, message: '发送财务系统不能为空！', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '数据状态不能为空！', trigger: 'blur' },
  ],

}
const isEdit = ref(true)
watch(() => formData, (newVal, oldVal) => {
  if(newVal.status === '0'){
  // if(newVal.status === '0' && newVal.redFlush === '1'){
    isEdit.value = true
  } else {
    isEdit.value = false
  }
},{immediate: false,deep:true})

const pCode = ref('')
// 初始化操作
onMounted(() => {
  initEHeadEdit();
  getPCode().then(res => {
    pCode.value = res;
    unitOptions.value = Object.entries(pCode.value.UNIT).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })
  getSupplierList();
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
});
// const initEHeadEdit = () => {
//   getStoreEHeadByHeadSid({'headId':props.headId}).then(res=>{
//     if (res.code === 200){
//       Object.assign(formData, res.data);
//       spinning.value = false
//     }else {
//       message.error(res.message)
//     }
//   })
// }
const initEHeadEdit = async () => {
  const res = await getStoreEHeadByHeadSid({'headId':props.headId})
  if (res.code === 200){
    Object.assign(formData, res.data);
    spinning.value = false
  }else {
    message.error(res.message)
  }
}

const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const unitOptions = ref([]);
const redFlushLoading = ref(false)
const confirmLoading = ref(false)
const confirmOrderHead = ()=>{
  handleConfirm()
}
const handleConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
  if (formData.status === '2'){
    message.warning('该数据已作废，不允许进行确认操作！')
    return
  }
  if (formData.status === '1'){
    message.warning('该数据已经确认，无需重复操作！')
    confirmLoading.value = false
    return
  }
  Modal.confirm({
    title: '确认操作',
    content: '是否确认所选项？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      confirmLoading.value = true; // 开始 loading
      // const sid = formData.sid;
      confirmStoreEHead(formData).then(res => {
        if (res.code === 200) {
          Object.assign(formData, res.data)
          // formData.status = '1'
          formData.createrUserName = res.data.updateUserName
          formData.createrTime = res.data.updateTime
          onBack({
            showBodyPurchaseHead: true,
            showBody: true,
            editStatus: editStatus.EDIT
          })
          message.success("确认成功！");
        } else {
          message.error(res.message || "确认失败，请重试！");
        }
      }).finally(() => {
        confirmLoading.value = false; // 结束 loading
      });
    },
    onCancel() {
      confirmLoading.value = false
    },
  });})
    .catch(error => {
      console.log('validate failed', error);
    })
};
const handlerRedFlush = () => {
  if (formData.status === '2'){
    message.warning('该数据已作废，不允许进行红冲操作！')
    return
  }
  if (formData.status === '1') {
    message.warning('该数据已经确认，不允许进行红冲操作');
    return;
  }
  if (formData.redFlush === '0') {
    message.warning('该数据已经红冲，无需重复操作');
    return;
  }
  Modal.confirm({
    title: '红冲操作',
    content: '是否红冲所选项？',
    okText: '红冲',
    cancelText: '取消',
    onOk() {
      redFlushLoading.value = true; // 开始 loading
      const sid = formData.sid;
      redFlushStoreEHead(sid).then(res => {
        if (res.code === 200) {
          message.success("红冲成功！");
          formData.redFlush = '0';
        } else {
          message.error(res.message || "红冲失败，请重试！");
        }
      }).finally(() => {
        redFlushLoading.value = false; // 结束 loading
      });
    }
  });
}

const supplierList = ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      handlerSaveLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertStoreEHeadList(formData).then((res)=>{
          if (res.code === 200){
            handlerSaveLoading.value = false
            message.success('新增成功!')
            // onBack(true)
          }else {
            handlerSaveLoading.value = false
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        updateStoreEHeadList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            Object.assign(formData, res.data)
            formData.createrUserName = res.data.updateUserName
            formData.createrTime = res.data.updateTime
            handlerSaveLoading.value = false
            message.success('修改成功!')
            // onBack(true)
          }else {
            message.error(res.message)
            handlerSaveLoading.value = false
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>



