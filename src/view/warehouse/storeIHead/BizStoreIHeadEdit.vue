<template>
  <section  >
    <a-card size="small" title="入库回单表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '160px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="storeINo" :label="'入库回单编号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.storeINo"/>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.purchaseOrderNo"/>
          </a-form-item>
          <a-form-item name="invoiceNo" :label="'发票号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.invoiceNo"/>
          </a-form-item>
          <a-form-item name="merchantCode" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.merchantCode" id="merchantCode">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="sellingRate" :label="'卖出价（汇率）'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.sellingRate" notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="foreignCurrPrice" :label="'外币货价'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.foreignCurrPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable || formData.status  !== '0' "
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="tariffPrice" :label="'关税'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.tariffPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="vatPrice" :label="'增值税'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.vatPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="agentFee" :label="'代理费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.agentFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="insuranceFee" :label="'保险费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.insuranceFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="insuranceFeeCurr" :label="'保险费用(外币)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.insuranceFeeCurr" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="productAmountTotal" :label="'商品金额小计'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.productAmountTotal" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="feeAmountTotal" :label="'费用金额小计'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.feeAmountTotal" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="taxAmountTotal" :label="'税金金额小计'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.taxAmountTotal" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="costAmountTotal" :label="'成本金额小计'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.costAmountTotal" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="totalPrice" :label="'合计金额（RMB）'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.totalPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable || formData.status  !== '0' "
              v-model:value="formData.businessDate"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="sendFinance" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendFinance" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="redFlush" :label="'是否红冲'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.redFlush" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.note"/>
          </a-form-item>
          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="status" :label="'数据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" v-if="!showDisable" :loading="handlerSaveLoading"
                      v-show="props.editConfig.editStatus !== 'SHOW' " :disabled="formData.status  !== '0' ">保存
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:confirm']">
              <a-button size="small" :loading="confirmLoading" @click="handlerConfirm" v-if="!showDisable"  v-show="props.editConfig.editStatus !== 'SHOW' ">
                <template #icon>
                  <GlobalIcon type="check" style="color:green"/>
                </template>
                确认
              </a-button>
            </div>
            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:redFlush']">
              <a-button size="small" :loading="redFlushLoading" @click="handlerRedFlush" v-if="!showDisable"  v-show="props.editConfig.editStatus !== 'SHOW' ">
                <template #icon>
                  <GlobalIcon type="up" style="color:red"/>
                </template>
                红冲
              </a-button>
            </div>
          </div>
        </a-form>
      </div>
    </a-card>

    <a-card size="small" title="入库回单表体" class="cs-card-form">
      <BizStoreIListList  :head-id="props.editConfig.editData.sid" :is-edit="formData.status" :showDisable="showDisable" :unit-options="unitOptions" ref="orderListRef"/>
    </a-card>

  </section>
</template>

<script setup>
import {editStatus,productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {getOrderSupplierList, insertStoreIHeadList, updateStoreIHeadList,confirmStoreIHead,redFlushStoreIHead} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import 'vue-multiselect/dist/vue-multiselect.min.css';
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import BizStoreIListList from "@/view/warehouse/storeIHead/list/BizStoreIListList";
import useEventBus from "@/view/common/eventBus";
const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
const confirmLoading = ref(false)
const redFlushLoading = ref(false)
const handlerSaveLoading = ref(false)
const unitOptions = ref([]);
const {emitEvent} = useEventBus()

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  sid:'',
  storeINo: '',
  contractNo: '',
  purchaseOrderNo: '',
  invoiceNo: '',
  merchantCode: '',
  sellingRate: '',
  foreignCurrPrice: '',
  curr: '',
  priceTerm: '',
  tariffPrice: '',
  vatPrice: '',
  agentFee: '',
  insuranceFee: '',
  insuranceFeeCurr: '',
  productAmountTotal: '',
  feeAmountTotal: '',
  taxAmountTotal: '',
  costAmountTotal: '',
  totalPrice: '',
  businessDate: '',
  sendFinance: '0',
  redFlush: '1',
  note: '',
  status: '0',
  createrBy: '',
  createrUserName: '',
  createrTime: '',

})
// 校验规则
const rules = {

  storeINo: [
    { max: 80, message: '入库回单编号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '入库回单编号不能为空！', trigger: 'blur' },
  ],
  contractNo: [
    { max: 80, message: '合同号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '合同号不能为空！', trigger: 'blur' },
  ],
  purchaseOrderNo: [
    { max: 80, message: '进货单号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '进货单号不能为空！', trigger: 'blur' },
  ],
  invoiceNo: [
    { max: 80, message: '发票号长度不能超过60位字节', trigger: 'blur'},
  ],
  merchantCode: [
    { required: true, message: '供应商不能为空！', trigger: 'blur' },
  ],
  foreignCurrPrice: [
    { required: true, message: '外币货价不能为空！', trigger: 'blur' },
  ],
  curr: [
    { required: true, message: '币种不能为空！', trigger: 'blur' },
  ],
  priceTerm: [{ required: true, message: '价格条款不能为空！', trigger: 'blur' },],
  sellingRate: [
    { required: true, message: '卖出价（汇率）不能为空！', trigger: 'blur' },
  ],
  tariffPrice: [
    { required: true, message: '关税不能为空！', trigger: 'blur' },
  ],
  vatPrice: [
    { required: true, message: '增值税不能为空！', trigger: 'blur' },
  ],
  productAmountTotal: [
    { required: true, message: '商品金额小计不能为空！', trigger: 'blur' },
  ],
  costAmountTotal: [
    { required: true, message: '成本金额小计不能为空！', trigger: 'blur' },
  ],
  totalPrice: [
    { required: true, message: '合计金额（RMB）不能为空！', trigger: 'blur' },
  ],
  redFlush: [
    { required: true, message: '是否红冲不能为空！', trigger: 'blur' },
  ],
  sendFinance: [
    { required: true, message: '发送财务系统不能为空！', trigger: 'blur' },
  ],
  businessDate: [
    { required: true, message: '业务日期不能为空！', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '单据状态不能为空！', trigger: 'blur' },
  ],
  note: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],
  createrUserName: [
    {required: true, message: '制单人不能为空！', trigger: 'blur'}
  ],
  createrTime: [
    {required: true, message: '制单时间不能为空！', trigger: 'blur'}
  ]

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getSupplierList();
  getPriceTermOptions();
  getPCode().then(res=>{
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
    unitOptions.value = Object.entries(pCode.value.UNIT).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});

const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const getPriceTermOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.priceTerms.listAll}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        priceTermList.value.push({
          value: item.paramCode,
          label: item.priceTerm
        });
      });
    }
  } catch (error) {
  }
}
const supplierList = ref([])
const currMap = ref([])
const priceTermList = ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      handlerSaveLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertStoreIHeadList(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            handlerSaveLoading.value = false
            // onBack(true)
          }else {
            message.error(res.message)
            handlerSaveLoading.value = false
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        updateStoreIHeadList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            handlerSaveLoading.value = false
            Object.assign(formData, res.data)
            formData.createrUserName = res.data.updateUserName
            formData.createrTime = res.data.updateTime
            onBack({
              editData: res.data,
              showBody: true,
              editStatus: editStatus.EDIT
            })
            emitEvent('refreshOrderList')
            // onBack(true)
          }else {
            handlerSaveLoading.value = false
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

/* 红冲处理 */
const handlerConfirm = () => {
  formRef.value
    .validate()
    .then(() => {
  if (formData.status === '2'){
    message.warning('该数据已作废，不允许进行确认操作！')
    return
  }
  if (formData.status === '1') {
    message.warning('该数据已经确认，无需重复操作');
    return;
  }

  Modal.confirm({
    title: '确认操作',
    content: '是否确认所选项？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      confirmLoading.value = true; // 开始 loading
      // const sid = formData.sid;
      confirmStoreIHead(formData).then(res => {
        if (res.code === 200) {
          message.success("确认成功！");
          Object.assign(formData, res.data)
          formData.createrUserName = res.data.updateUserName
          formData.createrTime = res.data.updateTime
          onBack({
            editData: res.data,
            showBodyPurchaseHead: true,
            showBody: true,
            editStatus: editStatus.EDIT,
            eHeadId:res.data.eheadId
          })
        } else {
          message.error(res.message || "确认失败，请重试！");
        }
      }).finally(() => {
        confirmLoading.value = false; // 结束 loading
      });
    },
    onCancel() {
      confirmLoading.value = false
    },
  });})
    .catch(error => {
      console.log('validate failed', error);
    })
}
/* 确认处理 */
const handlerRedFlush = () => {
  if (formData.status === '2'){
    message.warning('该数据已作废，不允许进行红冲操作！')
    return
  }
  if (formData.status === '1') {
    message.warning('该数据已经确认，不允许进行红冲操作');
    return;
  }
  if (formData.redFlush === '0') {
    message.warning('该数据已经红冲，无需重复操作');
    return;
  }
  Modal.confirm({
    title: '红冲操作',
    content: '是否红冲所选项？',
    okText: '红冲',
    cancelText: '取消',
    onOk() {
      redFlushLoading.value = true; // 开始 loading
      const sid = formData.sid;
      redFlushStoreIHead(sid).then(res => {
        if (res.code === 200) {
          message.success("红冲成功！");
          formData.redFlush = '0';
        } else {
          message.error(res.message || "红冲失败，请重试！");
        }
      }).finally(() => {
        redFlushLoading.value = false; // 结束 loading
      });
    }
  });
}


const allChange = () =>{
  productAmountTotalChange();
  feeAmountTotalChange();
  taxAmountTotalChange();
  costAmountTotalChange();
  totalPriceChange();
}
const feeAmountTotalChange = () =>{
  if(formData.feeAmountTotal !== null){
    delete formData.feeAmountTotal;
  }
  if(formData.agentFee !== null && formData.insuranceFee !== null){
    formData.feeAmountTotal = feeAmountTotalCount(formData)
  }
}
const taxAmountTotalChange = () =>{
  if(formData.taxAmountTotal !== null){
    delete formData.taxAmountTotal;
  }
  if(formData.tariffPrice !== null && formData.vatPrice !== null){
    formData.taxAmountTotal = taxAmountTotalCount(formData)
  }
}
const productAmountTotalChange = () =>{
  if(formData.productAmountTotal !== null){
    delete formData.productAmountTotal;
  }
  if(formData.foreignCurrPrice !== null && formData.sellingRate !== null){
    formData.productAmountTotal = productAmountTotalCount(formData)
  }
}
const costAmountTotalChange = () =>{
  if(formData.costAmountTotal !== null){
    delete formData.costAmountTotal;
  }
  if(formData.tariffPrice !== null && formData.feeAmountTotal !== null && formData.productAmountTotal !== null){
    formData.costAmountTotal = costAmountTotalCount(formData)
  }
}
const totalPriceChange = () =>{
  if(formData.totalPrice !== null){
    delete formData.totalPrice;
  }
  if(formData.tariffPrice !== null && formData.vatPrice !== null && formData.productAmountTotal !== null){
    formData.totalPrice = totalPriceCount(formData)
  }
}
const feeAmountTotalCount = (row) => {
  const agentFee = parseFloat(row.agentFee);
  const insuranceFee = parseFloat(row.insuranceFee);
  const feeAmountTotal = roundToDecimal(agentFee+insuranceFee,2)
  return feeAmountTotal !== null ? feeAmountTotal : null
};
const taxAmountTotalCount = (row) => {
  const tariffPrice = parseFloat(row.tariffPrice);
  const vatPrice = parseFloat(row.vatPrice);
  const taxAmountTotal = roundToDecimal(tariffPrice+vatPrice,2)
  return taxAmountTotal !== null ? taxAmountTotal : null
};
const costAmountTotalCount = (row) => {
  const productAmountTotal = parseFloat(row.productAmountTotal);
  const tariffPrice = parseFloat(row.tariffPrice);
  const feeAmountTotal = parseFloat(row.feeAmountTotal);
  const costAmountTotal = roundToDecimal(tariffPrice+productAmountTotal+feeAmountTotal,2)
  return costAmountTotal !== null ? costAmountTotal : null
};
const productAmountTotalCount = (row) => {
  const foreignCurrPrice = parseFloat(row. foreignCurrPrice);
  const sellingRate = parseFloat(row. sellingRate);
  const productAmountTotal = roundToDecimal(foreignCurrPrice*sellingRate,2)
  return productAmountTotal !== null ? productAmountTotal : null
};
const totalPriceCount = (row) => {
  const productAmountTotal = parseFloat(row.productAmountTotal);
  const tariffPrice = parseFloat(row.tariffPrice);
  const vatPrice = parseFloat(row.vatPrice);
  const totalPrice = roundToDecimal(tariffPrice+productAmountTotal+vatPrice,2)
  return totalPrice !== null ? totalPrice : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

</script>

<style lang="less" scoped>


</style>



