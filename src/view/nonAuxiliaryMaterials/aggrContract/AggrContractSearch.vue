<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

    <!-- 数据状态 -->
    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 购销合同号 -->
    <a-form-item name="contractNo"   :label="'购销合同号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>
    <!-- 购销年份 -->
    <a-form-item name="contractYear"   :label="'购销年份'" class="grid-item"  :colon="false">
      <a-date-picker
        v-model:value="searchParam.contractYear"
        id="contractYear"
        valueFormat="YYYY"
        format="YYYY"
        :locale="locale"
        picker="year"
        size="small"
        style="width: 100%"
        placeholder=""
      />
<!--      <a-input size="small" v-model:value="searchParam.contractNo" />-->
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder="制单日期止"
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'BuyContractSearch'
})
const searchParam = reactive({
  contractNo: '',
  insertTimeFrom: '',
  insertTimeTo: '',
  status: ''
})

const locale = {
}
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
